// PROCESS CONFIGURATION .tin FILE
// 98.08.14 R1.2 -> R2.0 Up Grade
// -------------------------------------------------------------------

// -------------------------------------------------------------------------------
// MONITOR PROCESS
// -------------------------------------------------------------------------------

/= process_no           1001
 = process_type         1
 = process_name         41_1_FTK_MON_SESSION____
 = execute_directory    /user/neoftk/command_logon_ftk/bin/
 = execute_program      mnt
 = command_line_1
 = command_line_2
 = command_line_3
 = command_line_4
 = command_line_5
 = send_group_no        0
 = monitor_process_no   0
 = logger_process_no    0
 = my_q_key             40001
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1

/= process_no           1002
 = process_type         1
 = process_name         41_1_FTK_MON_LOGONDB____
 = execute_directory    /user/neoftk/command_logon_ftk/bin/
 = execute_program      mnt
 = command_line_1
 = command_line_2
 = command_line_3
 = command_line_4
 = command_line_5
 = send_group_no        0
 = monitor_process_no   0
 = logger_process_no    0
 = my_q_key             40002
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1

/= process_no           1003
 = process_type         1
 = process_name         41_1_FTK_MON_SENDERDB___
 = execute_directory    /user/neoftk/command_logon_ftk/bin/
 = execute_program      mnt
 = command_line_1
 = command_line_2
 = command_line_3
 = command_line_4
 = command_line_5
 = send_group_no        0
 = monitor_process_no   0
 = logger_process_no    0
 = my_q_key             40003
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1
 
/= process_no           1004
 = process_type         1
 = process_name         41_1_FTK_MON_REPORTDB___
 = execute_directory    /user/neoftk/command_logon_ftk/bin/
 = execute_program      mnt
 = command_line_1
 = command_line_2
 = command_line_3
 = command_line_4
 = command_line_5
 = send_group_no        0
 = monitor_process_no   0
 = logger_process_no    0
 = my_q_key             40004
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1 
 
/= process_no           1005
 = process_type         1
 = process_name         41_1_FTK_MON_ADMIN______
 = execute_directory    /user/neoftk/command_logon_ftk/bin/
 = execute_program      mnt
 = command_line_1
 = command_line_2
 = command_line_3
 = command_line_4
 = command_line_5
 = send_group_no        0
 = monitor_process_no   0
 = logger_process_no    0
 = my_q_key             40005
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1
 
// -------------------------------------------------------------------------------
// CUSTOM PROCESS
// -------------------------------------------------------------------------------

/= process_no           3001
 = process_type         3
 = process_name         41_1_FTK_SESSION_43000__ 
 = execute_directory    /user/neoftk/daemon_logon_ftk/bin
 = execute_program      logonSession
 = command_line_1       /user/neoftk/cfg/kko_ftk/logonSession_ftk_43.conf
 = command_line_2
 = command_line_3
 = command_line_4
 = command_line_5
 = send_group_no        0
 = monitor_process_no   1001
 = logger_process_no    0
 = my_q_key             0
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1
 
/= process_no           3002
 = process_type         3
 = process_name         41_1_FTK_LOGONDB________
 = execute_directory    /user/neoftk/daemon_logon_ftk/bin
 = execute_program      logonDB
 = command_line_1       /user/neoftk/cfg/kko_ftk/logonDB.conf
 = command_line_2
 = command_line_3
 = command_line_4
 = command_line_5
 = send_group_no        0
 = monitor_process_no   1002
 = logger_process_no    0
 = my_q_key             0
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1

/= process_no           3003
 = process_type         3
 = process_name         41_1_FTK_SENDERDB_______
 = execute_directory    /user/neoftk/daemon_logon_ftk/bin
 = execute_program      senderMMSDB
 = command_line_1       /user/neoftk/cfg/kko_ftk/senderMMSDB.conf
 = command_line_2
 = command_line_3
 = command_line_4
 = command_line_5
 = send_group_no        0
 = monitor_process_no   1003
 = logger_process_no    0
 = my_q_key             0
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1
 
/= process_no           3004
 = process_type         3
 = process_name         41_1_FTK_REPORTDB_______
 = execute_directory    /user/neoftk/daemon_logon_ftk/bin
 = execute_program      reportMMSDB
 = command_line_1       /user/neoftk/cfg/kko_ftk/reportMMSDB.conf
 = command_line_2
 = command_line_3
 = command_line_4
 = command_line_5
 = send_group_no        0
 = monitor_process_no   1004
 = logger_process_no    0
 = my_q_key             0
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1  

/= process_no           3005
 = process_type         3
 = process_name         41_1_FTK_ADMIN__________
 = execute_directory    /user/neoftk/daemon_logon_ftk/bin
 = execute_program      adminProcess
 = command_line_1       /user/neoftk/cfg/kko_ftk/adminProcess.conf
 = command_line_2
 = command_line_3
 = command_line_4
 = command_line_5
 = send_group_no        0
 = monitor_process_no   1005
 = logger_process_no    0
 = my_q_key             0
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1

// -------------------------------------------------------------------------------
// WATCH DOG
// -------------------------------------------------------------------------------

/= process_no           9999
 = process_type         5
 = process_name         41_1_FTK_WATCHDOG_______
 = execute_directory    /user/neoftk/command_logon_ftk/bin
 = execute_program      dog
 = command_line_1       1
 = command_line_2
 = command_line_3
 = command_line_4
 = command_line_5
 = send_group_no        0
 = monitor_process_no   1001
 = logger_process_no    0
 = my_q_key             0
 = shm_key              0
 = sema_key             0
 = priority             7
 = start_up_flag        1

/* END */

cmake_minimum_required(VERSION 3.16)
project(daemon_sky_ftk)

set(CMAKE_CXX_STANDARD 11)

# 컴파일 데이터베이스 생성 (IDE/에디터의 코드 탐색 기능을 위해)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# OS 및 OpenSSL 버전 감지
execute_process(COMMAND lsb_release -rs OUTPUT_VARIABLE OS_VERSION OUTPUT_STRIP_TRAILING_WHITESPACE ERROR_QUIET)
execute_process(COMMAND cat /etc/redhat-release OUTPUT_VARIABLE REDHAT_RELEASE OUTPUT_STRIP_TRAILING_WHITESPACE ERROR_QUIET)

# OpenSSL 버전 확인
find_package(PkgConfig QUIET)
if(PkgConfig_FOUND)
    pkg_check_modules(OPENSSL openssl)
    if(OPENSSL_FOUND)
        set(OPENSSL_VERSION ${OPENSSL_VERSION})
    endif()
endif()

# OpenSSL 헤더에서 버전 직접 확인
if(NOT OPENSSL_VERSION)
    find_path(OPENSSL_INCLUDE_DIR openssl/opensslv.h)
    if(OPENSSL_INCLUDE_DIR)
        file(STRINGS "${OPENSSL_INCLUDE_DIR}/openssl/opensslv.h"
             OPENSSL_VERSION_LINE REGEX "^#define OPENSSL_VERSION_NUMBER")
        string(REGEX MATCH "0x[0-9a-fA-F]+" OPENSSL_VERSION_HEX "${OPENSSL_VERSION_LINE}")

        # OpenSSL 버전 분류
        if(OPENSSL_VERSION_HEX VERSION_GREATER_EQUAL "0x30000000")
            set(OPENSSL_MAJOR_VERSION 3)
        elseif(OPENSSL_VERSION_HEX VERSION_GREATER_EQUAL "0x10100000")
            set(OPENSSL_MAJOR_VERSION 1.1)
        else()
            set(OPENSSL_MAJOR_VERSION 1.0)
        endif()
    endif()
endif()

# OS별 컴파일 플래그 설정
# 모든 환경에서 OpenSSL 호환성 문제를 해결하기 위해 강제로 레거시 설정 적용
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -Wall -std=gnu++98 -w -Wno-deprecated-declarations")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DOPENSSL_API_COMPAT=0x10100000L -DOPENSSL_SUPPRESS_DEPRECATED")

if(REDHAT_RELEASE MATCHES "CentOS.*6\\.")
    # CentOS 6.x 환경 (OpenSSL 1.0.1)
    message(STATUS "Building for CentOS 6.x with OpenSSL 1.0.1")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DCENTOS_6X")
    set(LEGACY_OPENSSL TRUE)
elseif(REDHAT_RELEASE MATCHES "CentOS.*7\\.")
    # CentOS 7.x 환경 (OpenSSL 1.0.2)
    message(STATUS "Building for CentOS 7.x with OpenSSL 1.0.2")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DCENTOS_7X")
    set(LEGACY_OPENSSL TRUE)
elseif(REDHAT_RELEASE MATCHES "Rocky Linux.*9")
    # Rocky Linux 9 환경 (OpenSSL 3.x)
    message(STATUS "Building for Rocky Linux 9 with OpenSSL 3.x")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DROCKY_LINUX_9")
    set(MODERN_OPENSSL TRUE)
else()
    # 기타 환경 (OpenSSL 버전에 따라 결정)
    if(OPENSSL_MAJOR_VERSION STREQUAL "3")
        message(STATUS "Building with OpenSSL 3.x")
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DOPENSSL_3X")
        set(MODERN_OPENSSL TRUE)
    elseif(OPENSSL_MAJOR_VERSION STREQUAL "1.1")
        message(STATUS "Building with OpenSSL 1.1.x")
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DOPENSSL_11X")
        set(LEGACY_OPENSSL TRUE)
    else()
        message(STATUS "Building with OpenSSL 1.0.x or older")
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DOPENSSL_10X")
        set(LEGACY_OPENSSL TRUE)
    endif()
    # 기본값으로 MODERN_OPENSSL 설정 (OpenSSL 3.x 대응)
    if(NOT DEFINED LEGACY_OPENSSL)
        set(MODERN_OPENSSL TRUE)
    endif()
endif()

# 데이터베이스 접속 정보 설정
# Option 1: 환경변수 사용 (CI/CD 환경에 권장)
# Option 2: 별도 설정 파일 사용 (로컬 개발에 권장)

# 설정 파일이 존재하면 include
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/db_config.cmake")
    include("${CMAKE_CURRENT_SOURCE_DIR}/db_config.cmake")
    message(STATUS "Database config loaded from db_config.cmake")
endif()

# 환경변수에서 데이터베이스 정보 읽기 (설정 파일보다 우선)
if(DEFINED ENV{DBSTRING})
    set(DBSTRING "$ENV{DBSTRING}")
endif()
if(DEFINED ENV{DBID})
    set(DBID "$ENV{DBID}")
endif()
if(DEFINED ENV{DBPASS})
    set(DBPASS "$ENV{DBPASS}")
endif()

# 필수 변수 확인 (테스트 환경에서는 기본값 사용)
if(NOT DEFINED DBSTRING OR DBSTRING STREQUAL "")
    set(DBSTRING "test_db")
    message(STATUS "Using default DBSTRING: test_db")
endif()
if(NOT DEFINED DBID OR DBID STREQUAL "")
    set(DBID "test_user")
    message(STATUS "Using default DBID: test_user")
endif()
if(NOT DEFINED DBPASS OR DBPASS STREQUAL "")
    set(DBPASS "test_pass")
    message(STATUS "Using default DBPASS: test_pass")
endif()

# 디버그 정보 (실제 값은 출력하지 않음)
message(STATUS "Database configuration loaded successfully")

# Oracle 환경 설정
set(ORACLE_HOME "/usr/lib/oracle/21/client64")
set(PROC_INCLUDE "/usr/include/oracle/21/client64")
set(PROC_CONFIG "/usr/lib/oracle/21/client64/lib/precomp/admin/pcscfg.cfg")

# Oracle Pro*C 컴파일러 찾기
find_program(PROC_EXECUTABLE proc PATHS ${ORACLE_HOME}/bin)

# Oracle Pro*C 전처리 함수
function(add_proc_source target_name source_file)
    get_filename_component(source_name ${source_file} NAME_WE)

    set(pc_file ${CMAKE_CURRENT_BINARY_DIR}/${source_name}.pc)
    set(cpp_file ${CMAKE_CURRENT_BINARY_DIR}/${source_name}.cpp)

    # .cpp를 .pc로 복사
    add_custom_command(
        OUTPUT ${pc_file}
        COMMAND ${CMAKE_COMMAND} -E copy ${source_file} ${pc_file}
        DEPENDS ${source_file}
        COMMENT "Copying ${source_file} to ${pc_file}"
    )

    # 특정 파일들에만 THREADS=YES 사용 (필요에 따라 조건 수정 가능)
    if(source_name MATCHES "telco_sky_new|DatabaseORA")
        set(THREADS_OPTION "THREADS=YES")
    else()
        set(THREADS_OPTION "")
    endif()

    # Oracle Pro*C 전처리
    add_custom_command(
        OUTPUT ${cpp_file}
        COMMAND ${CMAKE_COMMAND} -E env
            "LD_LIBRARY_PATH=${ORACLE_HOME}/lib:$ENV{LD_LIBRARY_PATH}"
            "ORACLE_HOME=${ORACLE_HOME}"
            "TNS_ADMIN=${ORACLE_HOME}/network/admin"
            ${PROC_EXECUTABLE}
            MODE=ORACLE
            DBMS=V7
            UNSAFE_NULL=YES
            CHAR_MAP=STRING
            iname=${pc_file}
            include=${CMAKE_CURRENT_SOURCE_DIR}/inc
            include=${PROC_INCLUDE}
            include=${CMAKE_SOURCE_DIR}/../command_kskyb_ftk/inc
            ${THREADS_OPTION}
            CPP_SUFFIX=cpp
            CODE=CPP
            PARSE=NONE
            CTIMEOUT=3
            define=__sparc
            config=${PROC_CONFIG}
            SQLCHECK=SEMANTICS
            userid=${DBID}/${DBPASS}@${DBSTRING}
        DEPENDS ${pc_file}
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
        COMMENT "Processing ${pc_file} with Oracle Pro*C"
    )

    target_sources(${target_name} PRIVATE ${cpp_file})
endfunction()

# Include directories
include_directories(
    inc
    lib
    ${CMAKE_SOURCE_DIR}/../command_kskyb_ftk/inc
    ${CMAKE_SOURCE_DIR}/../libsrc/libkskyb/inc
    ${PROC_INCLUDE}
    $ENV{HOME}/library
)

# 라이브러리 디렉토리
link_directories(
    ${ORACLE_HOME}/lib
    /usr/lib64
    $ENV{HOME}/library
    ${ORAPP_DIR}
    ${LIBKSKYB_DIR}/lib
)

# 컴파일 정의
add_definitions(
    -D_GNU_SOURCE
    -D_REENTRANT
    -DDEBUG=5
)

# Oracle 컴파일 플래그
set(ORACLE_COMPILE_FLAGS
    "-D_SKY_MODE"
)

# 공통 라이브러리가 있다면 생성 (lib 디렉토리 확인 필요)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/lib)
    file(GLOB LIB_SOURCES "${CMAKE_CURRENT_SOURCE_DIR}/lib/*.cpp")
    # DatabaseORA.cpp는 Oracle Pro*C 파일이므로 별도 처리
    list(FILTER LIB_SOURCES EXCLUDE REGEX ".*DatabaseORA\\.cpp$")
    # AESEncryption.cpp는 더 이상 사용하지 않으므로 제외
    list(FILTER LIB_SOURCES EXCLUDE REGEX ".*AESEncryption\\.cpp$")
    if(LIB_SOURCES)
        add_library(daemon_sky_lib STATIC ${LIB_SOURCES})
    endif()
endif()

# DatabaseORA Oracle Pro*C 라이브러리 (별도 처리)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/lib/DatabaseORA.cpp)
    add_library(database_ora_sky STATIC)
    add_proc_source(database_ora_sky ${CMAKE_CURRENT_SOURCE_DIR}/lib/DatabaseORA.cpp)
    target_compile_options(database_ora_sky PRIVATE ${ORACLE_COMPILE_FLAGS})
endif()

# 메인 실행파일
add_executable(telco_sky_new)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/telco_sky_new.cpp)
    # 파일 내용을 읽어서 EXEC SQL이 있는지 확인
    file(READ ${CMAKE_CURRENT_SOURCE_DIR}/src/telco_sky_new.cpp TELCO_SKY_CONTENT)
    string(FIND "${TELCO_SKY_CONTENT}" "EXEC SQL" EXEC_SQL_FOUND)

    if(EXEC_SQL_FOUND GREATER -1)
        # EXEC SQL이 있으면 Pro*C 처리
        add_proc_source(telco_sky_new ${CMAKE_CURRENT_SOURCE_DIR}/src/telco_sky_new.cpp)
        target_compile_options(telco_sky_new PRIVATE ${ORACLE_COMPILE_FLAGS})
    else()
        # EXEC SQL이 없으면 일반 C++ 파일로 처리
        target_sources(telco_sky_new PRIVATE src/telco_sky_new.cpp)
    endif()
endif()

# 링크 라이브러리 설정
set(TARGETS telco_sky_new)

foreach(target ${TARGETS})
    target_include_directories(${target} PRIVATE inc)

    # 공통 라이브러리가 있다면 링크
    if(TARGET daemon_sky_lib)
        target_link_libraries(${target} daemon_sky_lib)
    endif()

    # DatabaseORA Oracle Pro*C 라이브러리 링크
    if(TARGET database_ora_sky)
        target_link_libraries(${target} database_ora_sky)
    endif()

    # command_kskyb_ftk의 오브젝트 파일이 있다면 링크
    if(EXISTS ${CMAKE_SOURCE_DIR}/../command_kskyb_ftk/obj/sms_ctrlsub++.o)
        target_link_libraries(${target} ${CMAKE_SOURCE_DIR}/../command_kskyb_ftk/obj/sms_ctrlsub++.o)
    endif()

    # libkskyb 라이브러리들 직접 링크 (makefile에서 사용하는 라이브러리들)
    target_link_libraries(${target}
        ${CMAKE_SOURCE_DIR}/../libsrc/libkskyb/lib/libksbase64.a
        ${CMAKE_SOURCE_DIR}/../libsrc/libkskyb/lib/libkssocket.a
        ${CMAKE_SOURCE_DIR}/../libsrc/libkskyb/lib/libksconfig.a
        ${CMAKE_SOURCE_DIR}/../libsrc/libkskyb/lib/libksthread.a
    )

    # orapp 라이브러리 직접 링크
    target_link_libraries(${target} ${CMAKE_SOURCE_DIR}/../libsrc/orapp/liborapp.a)

    # Oracle 관련 실행파일에는 Oracle 라이브러리 링크
    if(target MATCHES "telco_sky_new")
        target_link_libraries(${target} clntsh)
    endif()

    # OpenSSL crypto 라이브러리 링크 (Encrypt 클래스 사용)
    # 모든 환경에서 OpenSSL 3.x 호환성 문제를 해결하기 위해 강제로 레거시 설정 적용
    target_link_libraries(${target} crypto ssl)

    # OpenSSL 3.x에서 레거시 함수 사용을 위한 강력한 컴파일 플래그 추가
    target_compile_definitions(${target} PRIVATE
        OPENSSL_API_COMPAT=0x10100000L
        OPENSSL_SUPPRESS_DEPRECATED
        OPENSSL_NO_DEPRECATED_3_0
    )

    # 레거시 provider 활성화를 위한 추가 라이브러리 시도
    find_library(OPENSSL_LEGACY_LIB legacy PATHS /usr/lib64 /usr/lib /usr/lib/x86_64-linux-gnu)
    if(OPENSSL_LEGACY_LIB)
        target_link_libraries(${target} ${OPENSSL_LEGACY_LIB})
    endif()

    # OpenSSL 1.1 정적 라이브러리 시도
    find_library(OPENSSL_CRYPTO_11 crypto-1.1 PATHS /usr/lib64 /usr/lib /usr/lib/x86_64-linux-gnu)
    if(OPENSSL_CRYPTO_11)
        target_link_libraries(${target} ${OPENSSL_CRYPTO_11})
    endif()

    # 시스템 라이브러리 링크
    target_link_libraries(${target}
        pthread
        dl
        nsl
    )
endforeach()

# Set output directories
set_target_properties(${TARGETS}
                      PROPERTIES
                      RUNTIME_OUTPUT_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/bin"
)

# Add custom target for Makefile build
add_custom_target(makefile_build_daemon_sky
    COMMAND make -C ${CMAKE_CURRENT_SOURCE_DIR}/mak
    COMMENT "Building daemon sky with original Makefile"
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
)

# Add custom target for Makefile clean
add_custom_target(makefile_clean_daemon_sky
    COMMAND make -C ${CMAKE_CURRENT_SOURCE_DIR}/mak clean
    COMMENT "Cleaning daemon sky with original Makefile"
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
)

# libkskyb와 orapp 라이브러리 경로 설정 (빌드하지 않고 기존 라이브러리 사용)
set(LIBKSKYB_DIR ${CMAKE_SOURCE_DIR}/../libsrc/libkskyb)
set(ORAPP_DIR ${CMAKE_SOURCE_DIR}/../libsrc/orapp)

message(STATUS "CMake configuration completed successfully")

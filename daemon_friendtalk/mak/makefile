PROC=proc
CC=g++
COPY=cp
RM=rm
LINT=lint
CFLAGS = -g -Wall
CURLFLAGS = -g -Wall -D_URL_MODE
CMMSFLAGS = -g -Wall -D_MMS_MODE

# Database connection configuration
# Option 1: Use environment variables (recommended for CI/CD)
# Option 2: Use separate config file (recommended for local development)

# Try to include database config file if it exists
-include db_config.mk

# If config file doesn't exist, check environment variables
ifndef SMS_DBSTRING
ifdef DB_CONFIG_FILE
$(error Database config file db_config.mk not found. Copy db_config.mk.template to db_config.mk and set your values)
else
$(error SMS_DBSTRING not set. Either set environment variables or create db_config.mk from template)
endif
endif

ifndef SMS_DBID
$(error SMS_DBID not set. Either set environment variables or create db_config.mk from template)
endif

ifndef SMS_DBPASS
$(error SMS_DBPASS not set. Either set environment variables or create db_config.mk from template)
endif

ifndef MMS_DBSTRING
$(error MMS_DBSTRING not set. Either set environment variables or create db_config.mk from template)
endif

ifndef MMS_DBID
$(error MMS_DBID not set. Either set environment variables or create db_config.mk from template)
endif

ifndef MMS_DBPASS
$(error MMS_DBPASS not set. Either set environment variables or create db_config.mk from template)
endif

ORG_D=${HOME}/daemon_friendtalk
BIN_D=${ORG_D}/bin
OBJ_D=${ORG_D}/obj
LIB_D=${ORG_D}/lib
INC_D=${ORG_D}/inc
SRC_D=${ORG_D}/src

EXT_LIB=${HOME}/command_friendtalk/obj/sms_ctrlsub++.o
EXT_INC=${HOME}/command_friendtalk/inc

ORALIB1 = ${ORACLE_HOME}/lib
ORALIB2 = ${ORACLE_HOME}/plsql/lib
ORALIB3 = ${ORACLE_HOME}/network/lib
ORA_INC = ${ORACLE_HOME}/precomp/public

INCLUDE = $(PRECOMPPUBLIC) -I$(INC_D) -I$(LIB_D) -I$(ORA_INC) -I/usr/include/curl
LINKFLAGS = -L$(ORALIB1) -L$(ORALIB2) -L$(ORALIB3) -L$(ORA_INC) -L/usr/lib64
ORALIB = -lclntsh
LIBS = -lcurl -lpthread 

#all: friendtalk_mms ftalk_send_v2
#all: friendtalk_mms ftalk_send_v3
all: ftkup_send_v1

friendtalk_mms : $(OBJ_D)/friendtalk_mms.o $(OBJ_D)/Properties.o $(OBJ_D)/SocketTCP.o $(OBJ_D)/PacketCtrlSKB_MMS.o $(OBJ_D)/DatabaseORA_MMS.o $(OBJ_D)/myException.o $(OBJ_D)/Curl.o $(OBJ_D)/alimTalkApi.o $(OBJ_D)/jsoncpp.o
	${CC} $^ $(EXT_LIB) -I$(EXT_INC) $(INCLUDE) ${LINKFLAGS} ${SQL_INCLUDE} ${LIBS} ${PROLDLIBS} $(ORALIB) -o $(BIN_D)/friendtalk_mms

ftalk_send_v2 : $(OBJ_D)/ftalk_send_v2.o $(OBJ_D)/Properties.o $(OBJ_D)/SocketTCP.o $(OBJ_D)/PacketCtrlSKB_MMS.o $(OBJ_D)/DatabaseORA_MMS.o $(OBJ_D)/myException.o $(OBJ_D)/Curl.o $(OBJ_D)/alimTalkApi.o $(OBJ_D)/jsoncpp.o
	${CC} $^ $(EXT_LIB) -I$(EXT_INC) $(INCLUDE) ${LINKFLAGS} ${SQL_INCLUDE} ${LIBS} ${PROLDLIBS} $(ORALIB) -o $(BIN_D)/ftalk_send_v2

ftalk_send_v3 : $(OBJ_D)/ftalk_send_v3.o $(OBJ_D)/Properties.o $(OBJ_D)/DatabaseORA_MMS.o $(OBJ_D)/myException.o $(OBJ_D)/Curl.o $(OBJ_D)/alimTalkApi.o $(OBJ_D)/jsoncpp.o
	${CC} $(CFLAGS) $^ $(EXT_LIB) -I$(EXT_INC) $(INCLUDE) ${LINKFLAGS} ${SQL_INCLUDE} ${LIBS} ${PROLDLIBS} $(ORALIB) -o $(BIN_D)/ftalk_send_v3

ftkup_send_v1 : $(OBJ_D)/ftkup_send_v1.o $(OBJ_D)/Properties.o $(OBJ_D)/DatabaseORA_MMS.o $(OBJ_D)/myException.o $(OBJ_D)/Curl.o $(OBJ_D)/alimTalkApi.o $(OBJ_D)/jsoncpp.o
	${CC} $(CFLAGS) $^ $(EXT_LIB) -I$(EXT_INC) $(INCLUDE) ${LINKFLAGS} ${SQL_INCLUDE} ${LIBS} ${PROLDLIBS} $(ORALIB) -o $(BIN_D)/ftalk_send_v3

$(OBJ_D)/friendtalk_mms.o: $(SRC_D)/friendtalk_main.cpp
	$(RM) -rf $(OBJ_D)/friendtalk_mms.*
	$(COPY) $(SRC_D)/friendtalk_main.cpp $(OBJ_D)/friendtalk_mms.pc
	$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/friendtalk_mms.pc \
		include=$(INC_D) include=$(ORA_INC) \
		include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP PARSE=NONE CTIMEOUT=3 \
		define=__sparc SQLCHECK=SEMANTICS userid=$(MMS_DBID)/$(MMS_DBPASS)@$(MMS_DBSTRING)
	$(CC) -o $(OBJ_D)/friendtalk_mms.o -I${INCLUDE} -I$(EXT_INC) -c $(OBJ_D)/friendtalk_mms.cpp

$(OBJ_D)/ftalk_send_v2.o: $(SRC_D)/ftalk_send_v2.cpp
	$(RM) -rf $(OBJ_D)/ftalk_send_v2.*
	$(COPY) $(SRC_D)/ftalk_send_v2.cpp $(OBJ_D)/ftalk_send_v2.pc
	$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/ftalk_send_v2.pc \
		include=$(INC_D) include=$(ORA_INC) \
		include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP PARSE=NONE CTIMEOUT=3 \
		define=__sparc SQLCHECK=SEMANTICS userid=$(MMS_DBID)/$(MMS_DBPASS)@$(MMS_DBSTRING)
	$(CC) -o $(OBJ_D)/ftalk_send_v2.o -I${INCLUDE} -I$(EXT_INC) -c $(OBJ_D)/ftalk_send_v2.cpp

$(OBJ_D)/ftalk_send_v3.o: $(SRC_D)/ftalk_send_v3.cpp
	$(RM) -rf $(OBJ_D)/ftalk_send_v3.*
	$(COPY) $(SRC_D)/ftalk_send_v3.cpp $(OBJ_D)/ftalk_send_v3.pc
	$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/ftalk_send_v3.pc \
		include=$(INC_D) include=$(ORA_INC) \
		include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP PARSE=NONE CTIMEOUT=3 \
		define=__sparc SQLCHECK=SEMANTICS userid=$(MMS_DBID)/$(MMS_DBPASS)@$(MMS_DBSTRING)
	$(CC) $(CFLAGS) -o $(OBJ_D)/ftalk_send_v3.o -I${INCLUDE} -I$(EXT_INC) -c $(OBJ_D)/ftalk_send_v3.cpp

$(OBJ_D)/ftkup_send_v1.o: $(SRC_D)/ftkup_send_v1.cpp
	$(RM) -rf $(OBJ_D)/ftkup_send_v1.*
	$(COPY) $(SRC_D)/ftkup_send_v1.cpp $(OBJ_D)/ftkup_send_v1.pc
	$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/ftkup_send_v1.pc \
		include=$(INC_D) include=$(ORA_INC) \
		include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP PARSE=NONE CTIMEOUT=3 \
		define=__sparc SQLCHECK=SEMANTICS userid=$(MMS_DBID)/$(MMS_DBPASS)@$(MMS_DBSTRING)
	$(CC) $(CFLAGS) -o $(OBJ_D)/ftkup_send_v1.o -I${INCLUDE} -I$(EXT_INC) -c $(OBJ_D)/ftkup_send_v1.cpp
	
$(OBJ_D)/Properties.o: $(LIB_D)/Properties.cpp
	$(RM) -rf $(OBJ_D)/Properties.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/ksbase64.o: $(LIB_D)/ksbase64.cpp
	$(RM) -rf $(OBJ_D)/ksbase64.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/DatabaseORA_MMS.o: $(LIB_D)/DatabaseORA_MMS.cpp
	$(RM) -rf $(OBJ_D)/DatabaseORA_MMS.*
	$(COPY) $(LIB_D)/DatabaseORA_MMS.cpp $(OBJ_D)/DatabaseORA_MMS.pc
	$(PROC) mode=oracle dbms=v7 unsafe_null=yes char_map=string iname=$(OBJ_D)/DatabaseORA_MMS.pc \
		include=$(INC_D) include=$(ORA_INC) \
		include=$(EXT_INC) THREADS=YES CPP_SUFFIX=cpp CODE=CPP PARSE=NONE CTIMEOUT=3 \
		define=__sparc SQLCHECK=SEMANTICS userid=$(MMS_DBID)/$(MMS_DBPASS)@$(MMS_DBSTRING)
	$(CC) $(CMMSFLAGS) -o $(OBJ_D)/DatabaseORA_MMS.o $(CMMSFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $(OBJ_D)/DatabaseORA_MMS.cpp

$(OBJ_D)/myException.o: $(LIB_D)/myException.cpp
	$(RM) -rf $(OBJ_D)/myException.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/LogManager.o: $(LIB_D)/LogManager.cpp
	$(RM) -rf $(OBJ_D)/LogManager.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/Curl.o: $(LIB_D)/Curl.cpp
	$(RM) -rf $(OBJ_D)/Curl.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/alimTalkApi.o: $(LIB_D)/alimTalkApi.cpp
	$(RM) -rf $(OBJ_D)/alimTalkApi.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/jsoncpp.o: $(LIB_D)/jsoncpp.cpp
	$(RM) -rf $(OBJ_D)/jsoncpp.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

clean:
	rm  -rf $(OBJ_D)/*.o tp* $(OBJ_D)/*.lis $(OBJ_D)/*.pc $(OBJ_D)/*.cpp

install:
	mv $(BIN_D)/friendtalk_mms_tmp $(BIN_D)/friendtalk_mms
	rm tp*
